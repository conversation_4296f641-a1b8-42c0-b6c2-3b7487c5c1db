# Spring WebFlux MCP 服务器代理

一个基于 Spring WebFlux 的代理服务器，将基于 stdio 的 MCP（模型上下文协议）服务器转换为基于 HTTP 的 MCP 服务器，遵循 [open-webui/mcpo](https://github.com/open-webui/mcpo) 的架构模式。

## 概述

本实现提供了一个全面的解决方案，用于在沙箱环境中运行 MCP 服务器并通过 HTTP API 公开它们。包括：

- **用户认证**: 基于 JWT 的登录和注册功能
- **MCP 协议实现**: 完整的基于 JSON-RPC 2.0 的 MCP 协议支持
- **沙箱执行**: 基于 Docker 的沙箱，具有可扩展架构
- **配置管理**: 用于管理 MCP 服务器配置的 Web 界面
- **HTTP 代理**: 将 HTTP 请求转换为 MCP 协议消息
- **资源监控**: 沙箱资源使用的实时监控

## 架构

### 核心组件

1. **用户认证层** (`com.example.springvueapp.controller.AuthController`)
   - JWT 令牌生成和验证
   - 用户注册和登录
   - 基于 Spring Security 的安全控制

2. **MCP 协议层** (`com.example.springvueapp.mcp`)
   - JSON-RPC 2.0 消息处理
   - MCP 特定协议模型
   - stdio 通信的响应式客户端

3. **沙箱抽象** (`com.example.springvueapp.sandbox`)
   - `SandboxEnvironment`: 不同部署环境的抽象接口
   - `DockerSandboxEnvironment`: 基于 Docker 的实现
   - 为未来平台（Kubernetes、云函数等）的可扩展设计

4. **配置管理** (`com.example.springvueapp.service`)
   - MCP 服务器配置的 CRUD 操作
   - 验证和持久化
   - 用户范围的访问控制

5. **HTTP 代理服务** (`com.example.springvueapp.service.McpProxyService`)
   - 管理沙箱生命周期
   - 将 HTTP 请求路由到 MCP 服务器
   - 处理会话状态和错误恢复

### 前端组件

1. **Vue.js 管理界面**
   - 用户登录和注册界面
   - MCP 服务器配置管理 UI
   - 实时服务器监控
   - 工具测试界面
   - 资源使用可视化

## 主要功能

### 1. 用户认证和授权

- **JWT 认证**: 基于 JWT 令牌的身份验证
- **用户注册**: 新用户注册功能
- **用户登录**: 安全的用户登录
- **会话管理**: 自动令牌刷新和过期处理
- **权限控制**: 基于用户的资源访问控制

### 2. 沙箱执行环境

- **Docker 集成**: 使用 Docker Java 客户端进行容器管理
- **资源限制**: 可配置的 CPU、内存和磁盘限制
- **网络隔离**: 具有可配置访问的安全网络
- **卷挂载**: 支持持久数据和代码注入

### 3. MCP 协议支持

- **完整 MCP 兼容性**: 实现 MCP 规范 2024-11-05
- **工具发现**: 自动发现可用工具
- **工具执行**: 具有适当错误处理的响应式工具调用
- **服务器信息**: 访问服务器元数据和功能

### 4. 配置管理

- **Web 界面**: 用户友好的配置管理
- **验证**: 配置的全面验证
- **模板**: 常见运行时的预配置模板
- **版本控制**: 跟踪配置更改

### 5. HTTP-to-MCP 代理

- **RESTful API**: 所有 MCP 操作的标准 HTTP 端点
- **请求映射**: 自动将 HTTP 请求转换为 MCP 调用
- **错误处理**: 适当的 HTTP 状态码和错误响应
- **身份验证**: 与现有 Spring Security 集成

### 6. Server-Sent Events (SSE) 实时通信

- **实时消息流**: 基于 SSE 协议的 MCP 消息实时推送
- **双向通信**: 支持客户端向 MCP 服务器发送请求并接收响应
- **连接管理**: 自动连接管理、重连和超时处理
- **多路复用**: 支持同时连接多个 MCP 服务器实例
- **状态监控**: 实时监控连接状态和服务器健康状况
- **心跳检测**: 定期心跳检测确保连接稳定性

## API 端点

### 用户认证

```
POST   /auth/login                       # 用户登录
POST   /auth/register                    # 用户注册
GET    /users/{username}                 # 根据用户名查找用户
GET    /users/search                     # 搜索用户
POST   /users                           # 创建或更新用户
```

### 配置管理

```
GET    /api/mcp/configurations           # 列出用户配置
POST   /api/mcp/configurations           # 创建新配置
GET    /api/mcp/configurations/{id}      # 获取特定配置
PUT    /api/mcp/configurations/{id}      # 更新配置
DELETE /api/mcp/configurations/{id}      # 删除配置
POST   /api/mcp/configurations/{id}/toggle # 切换启用状态
```

### 服务器管理

```
POST   /api/mcp/proxy/start/{configId}   # 启动 MCP 服务器实例
POST   /api/mcp/proxy/stop/{sandboxId}   # 停止 MCP 服务器实例
GET    /api/mcp/proxy/instances          # 列出运行中的实例
GET    /api/mcp/proxy/instances/{id}/status    # 获取实例状态
GET    /api/mcp/proxy/instances/{id}/resources # 获取资源使用情况
```

### MCP 操作

```
GET    /api/mcp/proxy/{sandboxId}/tools         # 列出可用工具
GET    /api/mcp/proxy/{sandboxId}/info          # 获取服务器信息
POST   /api/mcp/proxy/{sandboxId}/tools/{tool}  # 调用特定工具
POST   /api/mcp/proxy/{sandboxId}/http/{tool}   # HTTP-to-MCP 代理
```

### MCP Server-Sent Events (SSE) 接口

```
GET    /api/mcp/sse/{sandboxId}/events          # 建立SSE连接，接收MCP消息流
POST   /api/mcp/sse/{sandboxId}/send            # 向MCP服务器发送JSON-RPC请求
GET    /api/mcp/sse/{sandboxId}/status          # 获取MCP服务器连接状态
POST   /api/mcp/sse/{sandboxId}/ping            # 心跳检测
POST   /api/mcp/sse/servers/{configId}/start    # 启动MCP服务器实例
POST   /api/mcp/sse/servers/{sandboxId}/stop    # 停止MCP服务器实例
POST   /api/mcp/sse/servers/{sandboxId}/restart # 重启MCP服务器实例
GET    /api/mcp/sse/servers                     # 获取用户的所有MCP服务器实例
```

## 使用流程

### 1. 用户注册和登录

1. **访问应用**: 打开浏览器访问 `http://localhost:3000`
2. **用户注册**: 如果是新用户，点击"注册"按钮创建账户
3. **用户登录**: 使用用户名和密码登录系统
4. **自动跳转**: 登录成功后自动跳转到 MCP 配置管理界面

### 2. MCP 服务器配置

1. **创建配置**: 在 MCP 仪表板中点击"添加新服务器"
2. **填写配置**:
   - 服务器名称和描述
   - Docker 镜像（如 `node:18-alpine`）
   - 启动命令和参数
   - 环境变量
   - 资源限制
3. **保存配置**: 验证并保存配置

### 3. 启动和管理 MCP 服务器

1. **启动服务器**: 在配置列表中点击"启动"按钮
2. **监控状态**: 实时查看服务器状态和资源使用情况
3. **测试工具**: 使用内置的工具测试界面验证功能
4. **查看日志**: 监控服务器运行日志和错误信息

### 4. 使用 MCP 服务

1. **发现工具**: 查看服务器提供的可用工具
2. **调用工具**: 通过 HTTP API 或 Web 界面调用工具
3. **监控性能**: 查看工具调用的响应时间和成功率

### 5. 使用 SSE 实时通信

1. **建立SSE连接**: 通过 `/api/mcp/sse/{sandboxId}/events` 建立实时消息流
2. **发送MCP请求**: 使用 `/api/mcp/sse/{sandboxId}/send` 发送JSON-RPC请求
3. **监控连接状态**: 通过 `/api/mcp/sse/{sandboxId}/status` 查看连接状态
4. **心跳检测**: 使用 `/api/mcp/sse/{sandboxId}/ping` 检测连接延迟

## 配置示例

### 基本 Node.js MCP 服务器

```json
{
  "name": "time-server",
  "description": "用于时间操作的 MCP 服务器",
  "command": "node",
  "arguments": ["server.js"],
  "dockerImage": "node:18-alpine",
  "workingDirectory": "/app",
  "environment": {
    "NODE_ENV": "production",
    "TZ": "Asia/Shanghai"
  },
  "resourceLimits": {
    "memoryLimitBytes": 536870912,
    "cpuLimit": 1.0
  },
  "timeoutSeconds": 300,
  "autoRestart": true
}
```

### 带卷挂载的 Python MCP 服务器

```json
{
  "name": "python-tools",
  "description": "基于 Python 的 MCP 工具",
  "command": "python",
  "arguments": ["-m", "mcp_server"],
  "dockerImage": "python:3.11-alpine",
  "workingDirectory": "/app",
  "volumeMounts": [
    {
      "hostPath": "/host/data",
      "containerPath": "/app/data",
      "readOnly": false
    }
  ],
  "resourceLimits": {
    "memoryLimitBytes": 1073741824,
    "cpuLimit": 2.0
  }
}
```

## SSE 使用示例

### JavaScript 客户端连接示例

```javascript
// 建立SSE连接
const sandboxId = 'your-sandbox-id';
const eventSource = new EventSource(`/api/mcp/sse/${sandboxId}/events`, {
  headers: {
    'Authorization': `Bearer ${jwtToken}`
  }
});

// 监听MCP消息
eventSource.addEventListener('mcp-message', (event) => {
  const message = JSON.parse(event.data);
  console.log('收到MCP消息:', message);

  if (message.type === 'response') {
    // 处理响应消息
    handleMcpResponse(message);
  } else if (message.type === 'notification') {
    // 处理通知消息
    handleMcpNotification(message);
  }
});

// 发送MCP请求
async function sendMcpRequest(method, params) {
  const request = {
    jsonrpc: '2.0',
    id: Date.now().toString(),
    method: method,
    params: params
  };

  const response = await fetch(`/api/mcp/sse/${sandboxId}/send`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${jwtToken}`
    },
    body: JSON.stringify(request)
  });

  return response.json();
}

// 检查连接状态
async function checkConnectionStatus() {
  const response = await fetch(`/api/mcp/sse/${sandboxId}/status`, {
    headers: {
      'Authorization': `Bearer ${jwtToken}`
    }
  });

  const status = await response.json();
  console.log('连接状态:', status);
  return status;
}

// 心跳检测
async function pingServer() {
  const response = await fetch(`/api/mcp/sse/${sandboxId}/ping`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${jwtToken}`
    }
  });

  const result = await response.json();
  console.log('心跳延迟:', result.latency, 'ms');
  return result;
}
```

### curl 命令示例

```bash
# 获取服务器状态
curl -H "Authorization: Bearer $JWT_TOKEN" \
     http://localhost:8080/api/mcp/sse/sandbox-123/status

# 发送MCP请求
curl -X POST \
     -H "Authorization: Bearer $JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"jsonrpc":"2.0","id":"1","method":"tools/list","params":{}}' \
     http://localhost:8080/api/mcp/sse/sandbox-123/send

# 心跳检测
curl -X POST \
     -H "Authorization: Bearer $JWT_TOKEN" \
     http://localhost:8080/api/mcp/sse/sandbox-123/ping

# 启动MCP服务器
curl -X POST \
     -H "Authorization: Bearer $JWT_TOKEN" \
     http://localhost:8080/api/mcp/sse/servers/1/start

# 获取用户的所有服务器
curl -H "Authorization: Bearer $JWT_TOKEN" \
     http://localhost:8080/api/mcp/sse/servers
```

## 安全考虑

### 沙箱安全

- **容器隔离**: 每个 MCP 服务器在自己的 Docker 容器中运行
- **资源限制**: 防止资源耗尽攻击
- **网络隔离**: 默认网络隔离，可配置访问
- **用户权限**: 容器内非 root 执行

### 访问控制

- **用户范围**: 配置和实例限定在已认证用户范围内
- **身份验证**: 与 Spring Security 集成
- **授权**: 管理功能的基于角色的访问控制

### 数据保护

- **输入验证**: 所有输入的全面验证
- **错误处理**: 安全的错误消息，不泄露信息
- **审计日志**: 所有操作的完整审计跟踪

## 部署

### 前提条件

- Java 17+
- Docker Engine
- Node.js 18+ (用于前端)
- Maven 3.6+

### 后端设置

```bash
cd backend
mvn clean install
mvn spring-boot:run
```

### 前端设置

```bash
cd frontend
npm install
npm run dev
```

### Docker 部署

```bash
# 构建后端
cd backend
mvn clean package
docker build -t mcp-proxy-backend .

# 构建前端
cd frontend
npm run build
docker build -t mcp-proxy-frontend .

# 使用 docker-compose 运行
docker-compose up -d
```

## 扩展点

### 添加新的沙箱环境

1. 实现 `SandboxEnvironment` 接口
2. 创建相应的 `SandboxInstance` 实现
3. 在 `McpConfig` 中添加配置 bean
4. 更新应用程序属性

Kubernetes 示例：

```java
@Component
@ConditionalOnProperty(name = "mcp.sandbox.type", havingValue = "kubernetes")
public class KubernetesSandboxEnvironment implements SandboxEnvironment {
    // Kubernetes pods 的实现
}
```

### 自定义 MCP 协议扩展

1. 在 `com.example.springvueapp.mcp.model` 中扩展 MCP 模型类
2. 为新消息类型更新 `McpClient`
3. 添加相应的 REST 端点

## 监控和可观测性

### 指标

- 沙箱创建/销毁率
- 每个实例的资源使用情况
- 工具调用成功/失败率
- 响应时间

### 日志

- 带有关联 ID 的结构化日志
- 不同组件的单独日志级别
- Docker 容器日志集成

### 健康检查

- Docker 守护进程连接性
- 数据库连接性
- 单个沙箱健康状况

## 未来增强

1. **Kubernetes 支持**: 原生 Kubernetes pod 执行
2. **云函数**: 无服务器执行环境
3. **负载均衡**: 同一 MCP 服务器的多个实例
4. **缓存**: 昂贵操作的响应缓存
5. **指标仪表板**: 实时监控界面
6. **自动扩缩**: 基于负载的动态扩缩
7. **插件系统**: 可扩展的插件架构

## 项目结构

```
spring-vue-app/
├── backend/                            # Spring Boot 后端
│   ├── src/main/java/com/example/springvueapp/
│   │   ├── config/                     # 配置类
│   │   ├── controller/                 # REST 控制器
│   │   │   ├── AuthController.java     # 用户认证控制器
│   │   │   ├── McpConfigurationController.java  # MCP 配置管理
│   │   │   ├── McpProxyController.java # MCP 代理控制器
│   │   │   └── McpSseController.java   # MCP SSE 实时通信控制器
│   │   ├── dto/                        # 前端数据传输对象
│   │   ├── entity/                     # 数据库实体类
│   │   ├── mapper/                     # DTO映射器
│   │   ├── mcp/                        # MCP 协议实现
│   │   │   ├── client/                 # MCP 客户端
│   │   │   ├── model/                  # MCP 数据模型
│   │   │   ├── protocol/               # JSON-RPC 协议
│   │   │   ├── routing/                # URL路由解析
│   │   │   ├── service/                # MCP 服务层
│   │   │   └── transport/              # 传输层实现
│   │   │       └── sse/                # SSE 传输实现
│   │   ├── model/                      # 数据实体
│   │   ├── repository/                 # 数据访问层
│   │   ├── sandbox/                    # 沙箱抽象层
│   │   │   └── docker/                 # Docker 实现
│   │   ├── service/                    # 业务逻辑层
│   │   └── SpringVueAppApplication.java
│   └── src/main/resources/
│       ├── application.properties      # 应用配置
│       └── schema.sql                  # 数据库架构
├── frontend/                           # Vue.js 前端
│   ├── src/
│   │   ├── components/                 # Vue 组件
│   │   │   ├── McpConfigurationModal.vue
│   │   │   ├── McpServerCard.vue
│   │   │   └── McpToolTestModal.vue
│   │   ├── store/                      # Pinia 状态管理
│   │   │   ├── auth.ts                 # 认证状态
│   │   │   └── mcp.ts                  # MCP 状态
│   │   ├── types/                      # TypeScript 类型
│   │   │   └── mcp.ts                  # MCP 类型定义
│   │   ├── views/                      # 页面组件
│   │   │   ├── LoginView.vue           # 登录页面
│   │   │   ├── HomeView.vue            # 主页
│   │   │   └── McpDashboard.vue        # MCP 仪表板
│   │   └── router/                     # 路由配置
│   └── package.json
├── docker-compose.yml                  # Docker 部署配置
└── README.md                          # 项目文档
```

## 技术栈

### 后端
- **Spring Boot 3.2.0** with WebFlux (响应式编程)
- **Java 17**
- **Maven** (构建工具)
- **H2 Database** (开发) / **PostgreSQL** (生产)
- **R2DBC** (响应式数据库访问)
- **Spring Security** (安全认证)
- **JWT** (身份验证)
- **Docker Java Client** (容器管理)

### 前端
- **Vue 3** (组合式 API)
- **TypeScript**
- **Vite** (构建工具)
- **Tailwind CSS** (样式框架)
- **Pinia** (状态管理)
- **Vue Router** (路由)
- **Axios** (HTTP 客户端)

## 贡献

1. Fork 仓库
2. 创建功能分支
3. 实现更改并编写测试
4. 提交 Pull Request

## 许可证

MIT 许可证 - 详情请参阅 LICENSE 文件
