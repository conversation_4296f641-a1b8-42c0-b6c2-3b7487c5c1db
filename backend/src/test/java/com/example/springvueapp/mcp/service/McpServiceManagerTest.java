package com.example.springvueapp.mcp.service;

import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.service.McpConfigurationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpServiceManager 单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpServiceManagerTest {

    @Mock
    private McpConfigurationService configurationService;

    @Mock
    private McpServiceProxy serviceProxy;

    private McpServiceManager serviceManager;

    @BeforeEach
    void setUp() {
        serviceManager = new McpServiceManager(configurationService, serviceProxy);
    }

    @Test
    void testHasAccess_UserOwnsServer() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(1L)
                .name("test-server")
                .userId(userId)
                .build();
        
        when(serviceProxy.getConfigurationBySandboxId(sandboxId))
                .thenReturn(Mono.just(config));

        // When & Then
        StepVerifier.create(serviceManager.hasAccess(sandboxId, userId))
                .expectNext(true)
                .verifyComplete();

        verify(serviceProxy).getConfigurationBySandboxId(sandboxId);
    }

    @Test
    void testHasAccess_UserDoesNotOwnServer() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        Long otherUserId = 2L;
        
        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(1L)
                .name("test-server")
                .userId(otherUserId)
                .build();
        
        when(serviceProxy.getConfigurationBySandboxId(sandboxId))
                .thenReturn(Mono.just(config));

        // When & Then
        StepVerifier.create(serviceManager.hasAccess(sandboxId, userId))
                .expectNext(false)
                .verifyComplete();

        verify(serviceProxy).getConfigurationBySandboxId(sandboxId);
    }

    @Test
    void testHasAccess_ServerNotFound() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceProxy.getConfigurationBySandboxId(sandboxId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(serviceManager.hasAccess(sandboxId, userId))
                .expectNext(false)
                .verifyComplete();

        verify(serviceProxy).getConfigurationBySandboxId(sandboxId);
    }

    @Test
    void testStartServer_Success() {
        // Given
        Long configId = 1L;
        Long userId = 1L;
        String sandboxId = "test-sandbox-456";
        
        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(configId)
                .name("test-server")
                .userId(userId)
                .dockerImage("node:18-alpine")
                .command("node")
                .build();
        
        when(configurationService.getConfiguration(configId, userId))
                .thenReturn(Mono.just(config));
        when(serviceProxy.startServer(config)).thenReturn(Mono.just(sandboxId));

        // When & Then
        StepVerifier.create(serviceManager.startServer(configId, userId))
                .expectNext(sandboxId)
                .verifyComplete();

        verify(configurationService).getConfiguration(configId, userId);
        verify(serviceProxy).startServer(config);
    }

    @Test
    void testStartServer_ConfigurationNotFound() {
        // Given
        Long configId = 1L;
        Long userId = 1L;
        
        when(configurationService.getConfiguration(configId, userId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(serviceManager.startServer(configId, userId))
                .expectErrorMatches(throwable -> 
                        throwable instanceof RuntimeException &&
                        throwable.getMessage().contains("配置不存在")
                )
                .verify();

        verify(configurationService).getConfiguration(configId, userId);
        verify(serviceProxy, never()).startServer(any());
    }

    @Test
    void testStopServer_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceProxy.stopServer(sandboxId)).thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(serviceManager.stopServer(sandboxId, userId))
                .verifyComplete();

        verify(serviceProxy).stopServer(sandboxId);
    }

    @Test
    void testRestartServer_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        String newSandboxId = "test-sandbox-456";
        
        McpServerConfiguration config = McpServerConfiguration.builder()
                .id(1L)
                .name("test-server")
                .userId(userId)
                .dockerImage("node:18-alpine")
                .command("node")
                .build();
        
        when(serviceProxy.getConfigurationBySandboxId(sandboxId))
                .thenReturn(Mono.just(config));
        when(serviceProxy.stopServer(sandboxId)).thenReturn(Mono.empty());
        when(serviceProxy.startServer(config)).thenReturn(Mono.just(newSandboxId));

        // When & Then
        StepVerifier.create(serviceManager.restartServer(sandboxId, userId))
                .expectNext(newSandboxId)
                .verifyComplete();

        verify(serviceProxy).getConfigurationBySandboxId(sandboxId);
        verify(serviceProxy).stopServer(sandboxId);
        verify(serviceProxy).startServer(config);
    }

    @Test
    void testGetUserServers_Success() {
        // Given
        Long userId = 1L;
        
        when(serviceProxy.getUserServers(userId))
                .thenReturn(Flux.just(
                        Map.of("sandboxId", "sandbox-1", "status", "running", "configId", 1L),
                        Map.of("sandboxId", "sandbox-2", "status", "stopped", "configId", 2L)
                ));

        // When & Then
        StepVerifier.create(serviceManager.getUserServers(userId))
                .expectNextCount(2)
                .verifyComplete();

        verify(serviceProxy).getUserServers(userId);
    }

    @Test
    void testGetServerStatus_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        
        Map<String, Object> expectedStatus = Map.of(
                "sandboxId", sandboxId,
                "status", "running",
                "uptime", 3600L
        );
        
        when(serviceProxy.getServerStatus(sandboxId))
                .thenReturn(Mono.just(expectedStatus));

        // When & Then
        StepVerifier.create(serviceManager.getServerStatus(sandboxId))
                .expectNext(expectedStatus)
                .verifyComplete();

        verify(serviceProxy).getServerStatus(sandboxId);
    }

    @Test
    void testGetServerLogs_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Integer lines = 100;
        
        when(serviceProxy.getServerLogs(sandboxId, lines))
                .thenReturn(Flux.just(
                        "Log line 1",
                        "Log line 2",
                        "Log line 3"
                ));

        // When & Then
        StepVerifier.create(serviceManager.getServerLogs(sandboxId, lines))
                .expectNextCount(3)
                .verifyComplete();

        verify(serviceProxy).getServerLogs(sandboxId, lines);
    }

    @Test
    void testIsServerRunning_Running() {
        // Given
        String sandboxId = "test-sandbox-123";
        
        when(serviceProxy.isServerRunning(sandboxId))
                .thenReturn(Mono.just(true));

        // When & Then
        StepVerifier.create(serviceManager.isServerRunning(sandboxId))
                .expectNext(true)
                .verifyComplete();

        verify(serviceProxy).isServerRunning(sandboxId);
    }

    @Test
    void testIsServerRunning_NotRunning() {
        // Given
        String sandboxId = "test-sandbox-123";
        
        when(serviceProxy.isServerRunning(sandboxId))
                .thenReturn(Mono.just(false));

        // When & Then
        StepVerifier.create(serviceManager.isServerRunning(sandboxId))
                .expectNext(false)
                .verifyComplete();

        verify(serviceProxy).isServerRunning(sandboxId);
    }

    @Test
    void testGetServerMetrics_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        
        Map<String, Object> expectedMetrics = Map.of(
                "cpuUsage", 25.5,
                "memoryUsage", 512L,
                "networkIO", Map.of("rx", 1024L, "tx", 2048L)
        );
        
        when(serviceProxy.getServerMetrics(sandboxId))
                .thenReturn(Mono.just(expectedMetrics));

        // When & Then
        StepVerifier.create(serviceManager.getServerMetrics(sandboxId))
                .expectNext(expectedMetrics)
                .verifyComplete();

        verify(serviceProxy).getServerMetrics(sandboxId);
    }
}
