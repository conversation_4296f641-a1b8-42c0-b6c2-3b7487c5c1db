package com.example.springvueapp.mcp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpServiceManager 单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpServiceManagerTest {
    
    @Mock
    private McpServiceProxy mockProxy1;
    
    @Mock
    private McpServiceProxy mockProxy2;
    
    private McpServiceManager mcpServiceManager;
    
    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_USER_ID = 1L;
    private static final Long TEST_CONFIG_ID = 1L;
    
    @BeforeEach
    void setUp() {
        // 设置模拟代理
        when(mockProxy1.getProxyType()).thenReturn("default-docker");
        when(mockProxy2.getProxyType()).thenReturn("kubernetes");
        
        List<McpServiceProxy> proxies = List.of(mockProxy1, mockProxy2);
        mcpServiceManager = new McpServiceManager(proxies);
    }
    
    @Test
    void testConstructor_SetsDefaultProxy() {
        // 验证默认代理设置
        assertNotNull(mcpServiceManager);
        assertEquals(List.of("default-docker", "kubernetes"), 
                mcpServiceManager.getRegisteredProxyTypes());
    }
    
    @Test
    void testStartMcpServer_Success() {
        // 准备测试数据
        McpServerConfiguration config = new McpServerConfiguration();
        config.setId(TEST_CONFIG_ID);
        config.setName("test-server");
        config.setDockerImage("test:latest");
        config.setUserId(TEST_USER_ID);
        
        McpServerInstance instance = new McpServerInstance();
        instance.setSandboxId(TEST_SANDBOX_ID);
        instance.setConfigurationId(TEST_CONFIG_ID);
        instance.setUserId(TEST_USER_ID);
        
        when(mockProxy1.startMcpServer(config, TEST_USER_ID))
                .thenReturn(Mono.just(instance));
        
        // 执行测试
        Mono<McpServerInstance> result = mcpServiceManager.startMcpServer(config, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(inst -> 
                    inst.getSandboxId().equals(TEST_SANDBOX_ID) &&
                    inst.getConfigurationId().equals(TEST_CONFIG_ID) &&
                    inst.getUserId().equals(TEST_USER_ID))
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).startMcpServer(config, TEST_USER_ID);
    }
    
    @Test
    void testStopMcpServer_Success() {
        // 准备测试数据
        when(mockProxy1.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(true));
        when(mockProxy1.stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.empty());
        when(mockProxy2.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<Void> result = mcpServiceManager.stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mockProxy1).stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
    }
    
    @Test
    void testStopMcpServer_NoProxyFound() {
        // 准备测试数据
        when(mockProxy1.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        when(mockProxy2.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<Void> result = mcpServiceManager.stopMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable instanceof RuntimeException && 
                    throwable.getMessage().contains("未找到处理该沙箱的代理"))
                .verify();
        
        // 验证方法调用
        verify(mockProxy1).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mockProxy2).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mockProxy1, never()).stopMcpServer(any(), any());
        verify(mockProxy2, never()).stopMcpServer(any(), any());
    }
    
    @Test
    void testGetMcpServerStatus_Success() {
        // 准备测试数据
        McpServerStatus status = new McpServerStatus();
        status.setSandboxId(TEST_SANDBOX_ID);
        status.setConnected(true);
        status.setLastActivity(LocalDateTime.now());
        
        when(mockProxy1.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(true));
        when(mockProxy1.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(status));
        when(mockProxy2.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<McpServerStatus> result = mcpServiceManager.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(s -> 
                    s.getSandboxId().equals(TEST_SANDBOX_ID) &&
                    s.isConnected())
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mockProxy1).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }
    
    @Test
    void testSendMcpRequest_Success() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("test-id");
        request.setMethod("test-method");
        
        JsonRpcResponse response = new JsonRpcResponse();
        response.setId("test-id");
        response.setResult("success");
        
        when(mockProxy1.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(true));
        when(mockProxy1.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request))
                .thenReturn(Mono.just(response));
        when(mockProxy2.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<JsonRpcResponse> result = mcpServiceManager.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(resp -> 
                    resp.getId().equals("test-id") &&
                    "success".equals(resp.getResult()))
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mockProxy1).sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
    }
    
    @Test
    void testGetUserMcpServers_Success() {
        // 准备测试数据
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setUserId(TEST_USER_ID);
        
        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setUserId(TEST_USER_ID);
        
        when(mockProxy1.getUserMcpServers(TEST_USER_ID))
                .thenReturn(Flux.just(instance1));
        when(mockProxy2.getUserMcpServers(TEST_USER_ID))
                .thenReturn(Flux.just(instance2));
        
        // 执行测试
        Flux<McpServerInstance> result = mcpServiceManager.getUserMcpServers(TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(inst -> inst.getSandboxId().equals("sandbox-1"))
                .expectNextMatches(inst -> inst.getSandboxId().equals("sandbox-2"))
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).getUserMcpServers(TEST_USER_ID);
        verify(mockProxy2).getUserMcpServers(TEST_USER_ID);
    }
    
    @Test
    void testHasAccess_Success() {
        // 准备测试数据
        when(mockProxy1.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(true));
        when(mockProxy2.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<Boolean> result = mcpServiceManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNext(true)
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        // mockProxy2 可能不会被调用，因为第一个代理已经返回true
    }
    
    @Test
    void testHasAccess_NoAccess() {
        // 准备测试数据
        when(mockProxy1.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        when(mockProxy2.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<Boolean> result = mcpServiceManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNext(false)
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mockProxy2).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
    }
    
    @Test
    void testRegisterProxy() {
        // 准备测试数据
        McpServiceProxy newProxy = mock(McpServiceProxy.class);
        when(newProxy.getProxyType()).thenReturn("new-proxy");
        
        // 执行测试
        mcpServiceManager.registerProxy(newProxy);
        
        // 验证结果
        assertTrue(mcpServiceManager.getRegisteredProxyTypes().contains("new-proxy"));
    }
    
    @Test
    void testCleanupUserMcpServers_Success() {
        // 准备测试数据
        when(mockProxy1.cleanupUserMcpServers(TEST_USER_ID))
                .thenReturn(Mono.empty());
        when(mockProxy2.cleanupUserMcpServers(TEST_USER_ID))
                .thenReturn(Mono.empty());
        
        // 执行测试
        Mono<Void> result = mcpServiceManager.cleanupUserMcpServers(TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();
        
        // 验证方法调用
        verify(mockProxy1).cleanupUserMcpServers(TEST_USER_ID);
        verify(mockProxy2).cleanupUserMcpServers(TEST_USER_ID);
    }
}
