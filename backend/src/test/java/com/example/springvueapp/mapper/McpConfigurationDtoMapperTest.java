package com.example.springvueapp.mapper;

import com.example.springvueapp.dto.McpServerConfigurationDto;
import com.example.springvueapp.dto.NetworkConfigDto;
import com.example.springvueapp.dto.ResourceLimitsDto;
import com.example.springvueapp.dto.VolumeMountDto;
import com.example.springvueapp.entity.McpServerConfigurationEntity;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.NetworkConfig;
import com.example.springvueapp.model.ResourceLimits;
import com.example.springvueapp.model.VolumeMount;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * McpConfigurationDtoMapper 单元测试
 */
class McpConfigurationDtoMapperTest {
    
    private McpConfigurationDtoMapper mapper;
    
    private static final Long TEST_ID = 1L;
    private static final String TEST_NAME = "test-server";
    private static final String TEST_DESCRIPTION = "测试服务器";
    private static final String TEST_DOCKER_IMAGE = "test:latest";
    private static final Long TEST_USER_ID = 1L;
    
    @BeforeEach
    void setUp() {
        mapper = new McpConfigurationDtoMapper();
    }
    
    @Test
    void testEntityToDto_Complete() {
        // 准备测试数据
        McpServerConfigurationEntity entity = createCompleteEntity();
        
        // 执行转换
        McpServerConfigurationDto dto = mapper.entityToDto(entity);
        
        // 验证结果
        assertNotNull(dto);
        assertEquals(TEST_ID, dto.getId());
        assertEquals(TEST_NAME, dto.getName());
        assertEquals(TEST_DESCRIPTION, dto.getDescription());
        assertEquals(TEST_DOCKER_IMAGE, dto.getDockerImage());
        assertEquals(TEST_USER_ID, dto.getUserId());
        assertTrue(dto.getEnabled());
        assertFalse(dto.getAutoRestart());
        
        // 验证资源限制
        assertNotNull(dto.getResourceLimits());
        assertEquals(512L * 1024 * 1024, dto.getResourceLimits().getMemoryLimitBytes());
        assertEquals(512, dto.getResourceLimits().getMemoryLimitMB());
        assertEquals(1.0, dto.getResourceLimits().getCpuLimit());
        
        // 验证网络配置
        assertNotNull(dto.getNetworkConfig());
        assertEquals("none", dto.getNetworkConfig().getNetworkMode());
        assertFalse(dto.getNetworkConfig().getEnableInternet());
        
        // 验证卷挂载
        assertNotNull(dto.getVolumeMounts());
        assertEquals(1, dto.getVolumeMounts().size());
        VolumeMountDto volumeMount = dto.getVolumeMounts().get(0);
        assertEquals("/host/path", volumeMount.getHostPath());
        assertEquals("/container/path", volumeMount.getContainerPath());
        assertTrue(volumeMount.getReadOnly());
    }
    
    @Test
    void testEntityToDto_Null() {
        // 测试null输入
        McpServerConfigurationDto dto = mapper.entityToDto(null);
        assertNull(dto);
    }
    
    @Test
    void testDtoToEntity_Complete() {
        // 准备测试数据
        McpServerConfigurationDto dto = createCompleteDto();
        
        // 执行转换
        McpServerConfigurationEntity entity = mapper.dtoToEntity(dto);
        
        // 验证结果
        assertNotNull(entity);
        assertEquals(TEST_ID, entity.getId());
        assertEquals(TEST_NAME, entity.getName());
        assertEquals(TEST_DESCRIPTION, entity.getDescription());
        assertEquals(TEST_DOCKER_IMAGE, entity.getDockerImage());
        assertEquals(TEST_USER_ID, entity.getUserId());
        assertTrue(entity.getEnabled());
        assertFalse(entity.getAutoRestart());
        
        // 验证资源限制
        assertNotNull(entity.getResourceLimits());
        assertEquals(512L * 1024 * 1024, entity.getResourceLimits().getMemoryLimitBytes());
        assertEquals(1.0, entity.getResourceLimits().getCpuLimit());
        
        // 验证网络配置
        assertNotNull(entity.getNetworkConfig());
        assertEquals("none", entity.getNetworkConfig().getNetworkMode());
        assertFalse(entity.getNetworkConfig().getEnableInternet());
        
        // 验证卷挂载
        assertNotNull(entity.getVolumeMounts());
        assertEquals(1, entity.getVolumeMounts().size());
        VolumeMount volumeMount = entity.getVolumeMounts().get(0);
        assertEquals("/host/path", volumeMount.getHostPath());
        assertEquals("/container/path", volumeMount.getContainerPath());
        assertTrue(volumeMount.getReadOnly());
    }
    
    @Test
    void testModelToDto_Complete() {
        // 准备测试数据
        McpServerConfiguration model = createCompleteModel();
        
        // 执行转换
        McpServerConfigurationDto dto = mapper.modelToDto(model);
        
        // 验证结果
        assertNotNull(dto);
        assertEquals(TEST_ID, dto.getId());
        assertEquals(TEST_NAME, dto.getName());
        assertEquals(TEST_DESCRIPTION, dto.getDescription());
        assertEquals(TEST_DOCKER_IMAGE, dto.getDockerImage());
        assertEquals(TEST_USER_ID, dto.getUserId());
        
        // 验证资源限制
        assertNotNull(dto.getResourceLimits());
        assertEquals(512L * 1024 * 1024, dto.getResourceLimits().getMemoryLimitBytes());
        assertEquals(1.0, dto.getResourceLimits().getCpuLimit());
    }
    
    @Test
    void testDtoToModel_Complete() {
        // 准备测试数据
        McpServerConfigurationDto dto = createCompleteDto();
        
        // 执行转换
        McpServerConfiguration model = mapper.dtoToModel(dto);
        
        // 验证结果
        assertNotNull(model);
        assertEquals(TEST_ID, model.getId());
        assertEquals(TEST_NAME, model.getName());
        assertEquals(TEST_DESCRIPTION, model.getDescription());
        assertEquals(TEST_DOCKER_IMAGE, model.getDockerImage());
        assertEquals(TEST_USER_ID, model.getUserId());
        
        // 验证资源限制
        assertNotNull(model.getResourceLimits());
        assertEquals(512L * 1024 * 1024, model.getResourceLimits().getMemoryLimitBytes());
        assertEquals(1.0, model.getResourceLimits().getCpuLimit());
    }
    
    @Test
    void testEntityListToDtoList() {
        // 准备测试数据
        List<McpServerConfigurationEntity> entities = List.of(
            createSimpleEntity(1L, "server1"),
            createSimpleEntity(2L, "server2")
        );
        
        // 执行转换
        List<McpServerConfigurationDto> dtos = mapper.entityListToDtoList(entities);
        
        // 验证结果
        assertNotNull(dtos);
        assertEquals(2, dtos.size());
        assertEquals("server1", dtos.get(0).getName());
        assertEquals("server2", dtos.get(1).getName());
    }
    
    @Test
    void testModelListToDtoList() {
        // 准备测试数据
        List<McpServerConfiguration> models = List.of(
            createSimpleModel(1L, "server1"),
            createSimpleModel(2L, "server2")
        );
        
        // 执行转换
        List<McpServerConfigurationDto> dtos = mapper.modelListToDtoList(models);
        
        // 验证结果
        assertNotNull(dtos);
        assertEquals(2, dtos.size());
        assertEquals("server1", dtos.get(0).getName());
        assertEquals("server2", dtos.get(1).getName());
    }
    
    @Test
    void testResourceLimitsMapping() {
        // 测试资源限制的双向转换
        ResourceLimitsDto originalDto = new ResourceLimitsDto();
        originalDto.setMemoryLimitMB(512);
        originalDto.setCpuLimit(1.5);
        originalDto.setMaxProcesses(100);
        
        // DTO -> Model -> DTO
        McpServerConfigurationDto configDto = new McpServerConfigurationDto();
        configDto.setResourceLimits(originalDto);
        
        McpServerConfiguration model = mapper.dtoToModel(configDto);
        McpServerConfigurationDto resultDto = mapper.modelToDto(model);
        
        // 验证转换结果
        assertNotNull(resultDto.getResourceLimits());
        assertEquals(512, resultDto.getResourceLimits().getMemoryLimitMB());
        assertEquals(512L * 1024 * 1024, resultDto.getResourceLimits().getMemoryLimitBytes());
        assertEquals(1.5, resultDto.getResourceLimits().getCpuLimit());
        assertEquals(100, resultDto.getResourceLimits().getMaxProcesses());
    }
    
    // 辅助方法
    private McpServerConfigurationEntity createCompleteEntity() {
        McpServerConfigurationEntity entity = new McpServerConfigurationEntity();
        entity.setId(TEST_ID);
        entity.setName(TEST_NAME);
        entity.setDescription(TEST_DESCRIPTION);
        entity.setDockerImage(TEST_DOCKER_IMAGE);
        entity.setUserId(TEST_USER_ID);
        entity.setEnabled(true);
        entity.setAutoRestart(false);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());
        
        // 资源限制
        ResourceLimits resourceLimits = new ResourceLimits();
        resourceLimits.setMemoryLimitBytes(512L * 1024 * 1024);
        resourceLimits.setCpuLimit(1.0);
        entity.setResourceLimits(resourceLimits);
        
        // 网络配置
        NetworkConfig networkConfig = new NetworkConfig();
        networkConfig.setNetworkMode("none");
        networkConfig.setEnableInternet(false);
        entity.setNetworkConfig(networkConfig);
        
        // 卷挂载
        VolumeMount volumeMount = new VolumeMount();
        volumeMount.setHostPath("/host/path");
        volumeMount.setContainerPath("/container/path");
        volumeMount.setReadOnly(true);
        entity.setVolumeMounts(List.of(volumeMount));
        
        return entity;
    }
    
    private McpServerConfigurationDto createCompleteDto() {
        McpServerConfigurationDto dto = new McpServerConfigurationDto();
        dto.setId(TEST_ID);
        dto.setName(TEST_NAME);
        dto.setDescription(TEST_DESCRIPTION);
        dto.setDockerImage(TEST_DOCKER_IMAGE);
        dto.setUserId(TEST_USER_ID);
        dto.setEnabled(true);
        dto.setAutoRestart(false);
        
        // 资源限制
        ResourceLimitsDto resourceLimits = new ResourceLimitsDto();
        resourceLimits.setMemoryLimitMB(512);
        resourceLimits.setCpuLimit(1.0);
        dto.setResourceLimits(resourceLimits);
        
        // 网络配置
        NetworkConfigDto networkConfig = new NetworkConfigDto();
        networkConfig.setNetworkMode("none");
        networkConfig.setEnableInternet(false);
        dto.setNetworkConfig(networkConfig);
        
        // 卷挂载
        VolumeMountDto volumeMount = new VolumeMountDto();
        volumeMount.setHostPath("/host/path");
        volumeMount.setContainerPath("/container/path");
        volumeMount.setReadOnly(true);
        dto.setVolumeMounts(List.of(volumeMount));
        
        return dto;
    }
    
    private McpServerConfiguration createCompleteModel() {
        McpServerConfiguration model = new McpServerConfiguration();
        model.setId(TEST_ID);
        model.setName(TEST_NAME);
        model.setDescription(TEST_DESCRIPTION);
        model.setDockerImage(TEST_DOCKER_IMAGE);
        model.setUserId(TEST_USER_ID);
        model.setEnabled(true);
        model.setAutoRestart(false);
        
        // 资源限制
        ResourceLimits resourceLimits = new ResourceLimits();
        resourceLimits.setMemoryLimitBytes(512L * 1024 * 1024);
        resourceLimits.setCpuLimit(1.0);
        model.setResourceLimits(resourceLimits);
        
        return model;
    }
    
    private McpServerConfigurationEntity createSimpleEntity(Long id, String name) {
        McpServerConfigurationEntity entity = new McpServerConfigurationEntity();
        entity.setId(id);
        entity.setName(name);
        entity.setDockerImage("test:latest");
        entity.setUserId(TEST_USER_ID);
        entity.setEnabled(true);
        return entity;
    }
    
    private McpServerConfiguration createSimpleModel(Long id, String name) {
        McpServerConfiguration model = new McpServerConfiguration();
        model.setId(id);
        model.setName(name);
        model.setDockerImage("test:latest");
        model.setUserId(TEST_USER_ID);
        model.setEnabled(true);
        return model;
    }
}
