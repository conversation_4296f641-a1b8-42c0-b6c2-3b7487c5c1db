package com.example.springvueapp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServiceManager;
import com.example.springvueapp.mcp.transport.sse.SseConnectionManager;
import com.example.springvueapp.mcp.transport.sse.SseServerTransport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpSseService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpSseServiceTest {

    @Mock
    private SseConnectionManager connectionManager;

    @Mock
    private McpServiceManager serviceManager;

    @Mock
    private SseServerTransport mockTransport;

    private McpSseService mcpSseService;

    @BeforeEach
    void setUp() {
        mcpSseService = new McpSseService(connectionManager, serviceManager);
    }

    @Test
    void testStreamMcpEvents_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(connectionManager.getOrCreateConnection(sandboxId, userId))
                .thenReturn(Mono.just(mockTransport));
        
        // Mock SSE事件流
        Flux<ServerSentEvent<String>> mockEventStream = Flux.just(
                ServerSentEvent.<String>builder()
                        .event("mcp-message")
                        .data("{\"type\":\"response\",\"id\":\"1\",\"result\":{}}")
                        .build()
        );
        
        when(mockTransport.getEventStream()).thenReturn(mockEventStream);

        // When & Then
        StepVerifier.create(mcpSseService.streamMcpEvents(sandboxId, userId))
                .expectNextMatches(event -> 
                        "mcp-message".equals(event.event()) &&
                        event.data() != null &&
                        event.data().contains("\"type\":\"response\"")
                )
                .verifyComplete();

        verify(serviceManager).hasAccess(sandboxId, userId);
        verify(connectionManager).getOrCreateConnection(sandboxId, userId);
    }

    @Test
    void testStreamMcpEvents_AccessDenied() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(false));

        // When & Then
        StepVerifier.create(mcpSseService.streamMcpEvents(sandboxId, userId))
                .expectNextMatches(event -> 
                        "error".equals(event.event()) &&
                        event.data() != null &&
                        event.data().contains("访问被拒绝")
                )
                .verifyComplete();

        verify(serviceManager).hasAccess(sandboxId, userId);
        verify(connectionManager, never()).getOrCreateConnection(any(), any());
    }

    @Test
    void testSendMcpRequest_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        JsonRpcRequest request = new JsonRpcRequest("1", "tools/list", Map.of());
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(connectionManager.getConnection(sandboxId, userId))
                .thenReturn(Mono.just(mockTransport));
        when(mockTransport.sendToServer(request)).thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(mcpSseService.sendMcpRequest(sandboxId, userId, request))
                .expectNext(Map.of("status", "sent", "requestId", "1"))
                .verifyComplete();

        verify(serviceManager).hasAccess(sandboxId, userId);
        verify(connectionManager).getConnection(sandboxId, userId);
        verify(mockTransport).sendToServer(request);
    }

    @Test
    void testSendMcpRequest_NoConnection() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        JsonRpcRequest request = new JsonRpcRequest("1", "tools/list", Map.of());
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(connectionManager.getConnection(sandboxId, userId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(mcpSseService.sendMcpRequest(sandboxId, userId, request))
                .expectErrorMatches(throwable -> 
                        throwable instanceof RuntimeException &&
                        throwable.getMessage().contains("连接不存在")
                )
                .verify();

        verify(serviceManager).hasAccess(sandboxId, userId);
        verify(connectionManager).getConnection(sandboxId, userId);
        verify(mockTransport, never()).sendToServer(any());
    }

    @Test
    void testGetConnectionStatus_Connected() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(connectionManager.getConnection(sandboxId, userId))
                .thenReturn(Mono.just(mockTransport));
        when(mockTransport.isConnected()).thenReturn(true);

        // When & Then
        StepVerifier.create(mcpSseService.getConnectionStatus(sandboxId, userId))
                .expectNextMatches(status -> 
                        "connected".equals(status.get("status")) &&
                        Boolean.TRUE.equals(status.get("connected"))
                )
                .verifyComplete();
    }

    @Test
    void testGetConnectionStatus_NotConnected() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(connectionManager.getConnection(sandboxId, userId))
                .thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(mcpSseService.getConnectionStatus(sandboxId, userId))
                .expectNextMatches(status -> 
                        "disconnected".equals(status.get("status")) &&
                        Boolean.FALSE.equals(status.get("connected"))
                )
                .verifyComplete();
    }

    @Test
    void testPingConnection_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(connectionManager.getConnection(sandboxId, userId))
                .thenReturn(Mono.just(mockTransport));
        when(mockTransport.ping()).thenReturn(Mono.just(Duration.ofMillis(50)));

        // When & Then
        StepVerifier.create(mcpSseService.pingConnection(sandboxId, userId))
                .expectNextMatches(result -> 
                        "pong".equals(result.get("status")) &&
                        result.get("latency") != null
                )
                .verifyComplete();
    }

    @Test
    void testStartMcpServer_Success() {
        // Given
        Long configId = 1L;
        Long userId = 1L;
        String sandboxId = "test-sandbox-456";
        
        when(serviceManager.startServer(configId, userId))
                .thenReturn(Mono.just(sandboxId));

        // When & Then
        StepVerifier.create(mcpSseService.startMcpServer(configId, userId))
                .expectNextMatches(result -> 
                        "started".equals(result.get("status")) &&
                        sandboxId.equals(result.get("sandboxId"))
                )
                .verifyComplete();

        verify(serviceManager).startServer(configId, userId);
    }

    @Test
    void testStopMcpServer_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(serviceManager.stopServer(sandboxId, userId)).thenReturn(Mono.empty());
        when(connectionManager.closeConnection(sandboxId, userId)).thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(mcpSseService.stopMcpServer(sandboxId, userId))
                .expectNextMatches(result -> 
                        "stopped".equals(result.get("status")) &&
                        sandboxId.equals(result.get("sandboxId"))
                )
                .verifyComplete();

        verify(serviceManager).hasAccess(sandboxId, userId);
        verify(serviceManager).stopServer(sandboxId, userId);
        verify(connectionManager).closeConnection(sandboxId, userId);
    }

    @Test
    void testRestartMcpServer_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        when(serviceManager.hasAccess(sandboxId, userId)).thenReturn(Mono.just(true));
        when(serviceManager.restartServer(sandboxId, userId)).thenReturn(Mono.just(sandboxId));
        when(connectionManager.closeConnection(sandboxId, userId)).thenReturn(Mono.empty());

        // When & Then
        StepVerifier.create(mcpSseService.restartMcpServer(sandboxId, userId))
                .expectNextMatches(result -> 
                        "restarted".equals(result.get("status")) &&
                        sandboxId.equals(result.get("sandboxId"))
                )
                .verifyComplete();

        verify(serviceManager).hasAccess(sandboxId, userId);
        verify(serviceManager).restartServer(sandboxId, userId);
        verify(connectionManager).closeConnection(sandboxId, userId);
    }

    @Test
    void testGetUserServers_Success() {
        // Given
        Long userId = 1L;
        
        when(serviceManager.getUserServers(userId))
                .thenReturn(Flux.just(
                        Map.of("sandboxId", "sandbox-1", "status", "running"),
                        Map.of("sandboxId", "sandbox-2", "status", "stopped")
                ));

        // When & Then
        StepVerifier.create(mcpSseService.getUserServers(userId))
                .expectNextCount(2)
                .verifyComplete();

        verify(serviceManager).getUserServers(userId);
    }
}
