package com.example.springvueapp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServiceManager;
import com.example.springvueapp.mcp.transport.sse.SseConnectionManager;
import com.example.springvueapp.mcp.transport.sse.SseServerTransport;
import com.example.springvueapp.model.McpServerStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.LocalDateTime;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * McpSseService 单元测试
 */
@ExtendWith(MockitoExtension.class)
class McpSseServiceTest {
    
    @Mock
    private McpServiceManager mcpServiceManager;
    
    @Mock
    private SseConnectionManager sseConnectionManager;
    
    @Mock
    private SseServerTransport sseServerTransport;
    
    private ObjectMapper objectMapper;
    private McpSseService mcpSseService;
    
    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_USER_ID = 1L;
    
    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        mcpSseService = new McpSseService(mcpServiceManager, sseConnectionManager, objectMapper);
    }
    
    @Test
    void testStreamMcpEvents_Success() {
        // 准备测试数据
        when(mcpServiceManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(true));
        when(sseConnectionManager.createConnection(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(sseServerTransport));
        when(sseServerTransport.getOutgoingMessageStream())
                .thenReturn(Flux.just("{\"type\":\"test\",\"message\":\"hello\"}"));
        when(mcpServiceManager.getMcpMessageStream(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Flux.just("{\"type\":\"mcp\",\"data\":\"test\"}"));
        
        // 执行测试
        Flux<String> result = mcpSseService.streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNext("{\"type\":\"test\",\"message\":\"hello\"}")
                .verifyComplete();
        
        // 验证方法调用
        verify(mcpServiceManager).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(sseConnectionManager).createConnection(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(sseServerTransport).getOutgoingMessageStream();
    }
    
    @Test
    void testStreamMcpEvents_AccessDenied() {
        // 准备测试数据
        when(mcpServiceManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Flux<String> result = mcpSseService.streamMcpEvents(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable instanceof RuntimeException && 
                    "访问被拒绝".equals(throwable.getMessage()))
                .verify();
        
        // 验证方法调用
        verify(mcpServiceManager).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(sseConnectionManager, never()).createConnection(any(), any());
    }
    
    @Test
    void testSendMcpRequest_Success() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("test-id");
        request.setMethod("test-method");
        
        when(mcpServiceManager.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request))
                .thenReturn(Mono.empty());
        
        // 执行测试
        Mono<Void> result = mcpSseService.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
        
        // 验证结果
        StepVerifier.create(result)
                .verifyComplete();
        
        // 验证方法调用
        verify(mcpServiceManager).sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
    }
    
    @Test
    void testSendMcpRequest_Error() {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("test-id");
        request.setMethod("test-method");
        
        RuntimeException testError = new RuntimeException("发送失败");
        when(mcpServiceManager.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request))
                .thenReturn(Mono.error(testError));
        
        // 执行测试
        Mono<Void> result = mcpSseService.sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
        
        // 验证结果
        StepVerifier.create(result)
                .expectError(RuntimeException.class)
                .verify();
        
        // 验证方法调用
        verify(mcpServiceManager).sendMcpRequest(TEST_SANDBOX_ID, TEST_USER_ID, request);
    }
    
    @Test
    void testGetMcpServerStatus_Success() {
        // 准备测试数据
        McpServerStatus status = new McpServerStatus();
        status.setSandboxId(TEST_SANDBOX_ID);
        status.setConnected(true);
        status.setLastActivity(LocalDateTime.now());
        status.setActiveConnections(0);
        
        when(mcpServiceManager.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(status));
        when(sseConnectionManager.getSandboxActiveConnectionCount(TEST_SANDBOX_ID))
                .thenReturn(2);
        
        // 执行测试
        Mono<McpServerStatus> result = mcpSseService.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(s -> 
                    s.getSandboxId().equals(TEST_SANDBOX_ID) &&
                    s.isConnected() &&
                    s.getActiveConnections() == 2)
                .verifyComplete();
        
        // 验证方法调用
        verify(mcpServiceManager).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(sseConnectionManager).getSandboxActiveConnectionCount(TEST_SANDBOX_ID);
    }
    
    @Test
    void testPingMcpServer_Success() {
        // 准备测试数据
        McpServerStatus status = new McpServerStatus();
        status.setSandboxId(TEST_SANDBOX_ID);
        status.setConnected(true);
        
        when(mcpServiceManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(true));
        when(mcpServiceManager.getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(status));
        
        // 执行测试
        Mono<Long> result = mcpSseService.pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectNextMatches(latency -> latency >= 0)
                .verifyComplete();
        
        // 验证方法调用
        verify(mcpServiceManager).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mcpServiceManager).getMcpServerStatus(TEST_SANDBOX_ID, TEST_USER_ID);
    }
    
    @Test
    void testPingMcpServer_AccessDenied() {
        // 准备测试数据
        when(mcpServiceManager.hasAccess(TEST_SANDBOX_ID, TEST_USER_ID))
                .thenReturn(Mono.just(false));
        
        // 执行测试
        Mono<Long> result = mcpSseService.pingMcpServer(TEST_SANDBOX_ID, TEST_USER_ID);
        
        // 验证结果
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable instanceof RuntimeException && 
                    "访问被拒绝".equals(throwable.getMessage()))
                .verify();
        
        // 验证方法调用
        verify(mcpServiceManager).hasAccess(TEST_SANDBOX_ID, TEST_USER_ID);
        verify(mcpServiceManager, never()).getMcpServerStatus(any(), any());
    }
}
