package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServerLifecycleManager;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import com.example.springvueapp.service.McpSseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * McpSseController 单元测试
 */
@WebFluxTest(McpSseController.class)
class McpSseControllerTest {
    
    @Autowired
    private WebTestClient webTestClient;
    
    @MockBean
    private McpSseService mcpSseService;
    
    @MockBean
    private McpServerLifecycleManager lifecycleManager;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    private static final String TEST_SANDBOX_ID = "test-sandbox-123";
    private static final Long TEST_CONFIG_ID = 1L;
    private static final Long TEST_USER_ID = 1L;
    
    @BeforeEach
    void setUp() {
        // 设置通用的模拟行为
    }
    
    @Test
    @WithMockUser(username = "1")
    void testStreamMcpEvents_Success() {
        // 准备测试数据
        when(mcpSseService.streamMcpEvents(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Flux.just(
                    "{\"type\":\"connected\",\"message\":\"连接已建立\"}",
                    "{\"type\":\"data\",\"content\":\"test message\"}"
                ));
        
        // 执行测试
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/events", TEST_SANDBOX_ID)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentType(MediaType.TEXT_EVENT_STREAM)
                .expectBodyList(String.class)
                .hasSize(2);
    }
    
    @Test
    @WithMockUser(username = "1")
    void testSendMcpRequest_Success() throws Exception {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("test-id");
        request.setMethod("test-method");
        request.setParams(Map.of("param1", "value1"));
        
        when(mcpSseService.sendMcpRequest(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID), any(JsonRpcRequest.class)))
                .thenReturn(Mono.empty());
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", TEST_SANDBOX_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("请求已发送")
                .jsonPath("$.requestId").isEqualTo("test-id");
    }
    
    @Test
    @WithMockUser(username = "1")
    void testSendMcpRequest_Error() throws Exception {
        // 准备测试数据
        JsonRpcRequest request = new JsonRpcRequest();
        request.setId("test-id");
        request.setMethod("test-method");
        
        when(mcpSseService.sendMcpRequest(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID), any(JsonRpcRequest.class)))
                .thenReturn(Mono.error(new RuntimeException("发送失败")));
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", TEST_SANDBOX_ID)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").isEqualTo("发送失败");
    }
    
    @Test
    @WithMockUser(username = "1")
    void testGetMcpServerStatus_Success() {
        // 准备测试数据
        McpServerStatus status = new McpServerStatus();
        status.setSandboxId(TEST_SANDBOX_ID);
        status.setConnected(true);
        status.setLastActivity(LocalDateTime.now());
        status.setActiveConnections(2);
        
        when(mcpSseService.getMcpServerStatus(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.just(status));
        
        // 执行测试
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/status", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.sandboxId").isEqualTo(TEST_SANDBOX_ID)
                .jsonPath("$.connected").isEqualTo(true)
                .jsonPath("$.activeConnections").isEqualTo(2);
    }
    
    @Test
    @WithMockUser(username = "1")
    void testGetMcpServerStatus_Error() {
        // 准备测试数据
        when(mcpSseService.getMcpServerStatus(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.error(new RuntimeException("获取状态失败")));
        
        // 执行测试
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/status", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.sandboxId").isEqualTo(TEST_SANDBOX_ID)
                .jsonPath("$.connected").isEqualTo(false)
                .jsonPath("$.error").isEqualTo("获取状态失败");
    }
    
    @Test
    @WithMockUser(username = "1")
    void testPingMcpServer_Success() {
        // 准备测试数据
        when(mcpSseService.pingMcpServer(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.just(50L));
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/ping", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.latency").isEqualTo(50);
    }
    
    @Test
    @WithMockUser(username = "1")
    void testPingMcpServer_Error() {
        // 准备测试数据
        when(mcpSseService.pingMcpServer(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.error(new RuntimeException("ping失败")));
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/ping", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(false)
                .jsonPath("$.error").isEqualTo("ping失败");
    }
    
    @Test
    @WithMockUser(username = "1")
    void testStartMcpServer_Success() {
        // 准备测试数据
        McpServerInstance instance = new McpServerInstance();
        instance.setSandboxId(TEST_SANDBOX_ID);
        instance.setConfigurationId(TEST_CONFIG_ID);
        instance.setUserId(TEST_USER_ID);
        
        when(lifecycleManager.startServer(eq(TEST_CONFIG_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.just(instance));
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/servers/{configId}/start", TEST_CONFIG_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("服务器启动成功")
                .jsonPath("$.instance.sandboxId").isEqualTo(TEST_SANDBOX_ID);
    }
    
    @Test
    @WithMockUser(username = "1")
    void testStopMcpServer_Success() {
        // 准备测试数据
        when(lifecycleManager.stopServer(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.empty());
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/servers/{sandboxId}/stop", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("服务器停止成功")
                .jsonPath("$.sandboxId").isEqualTo(TEST_SANDBOX_ID);
    }
    
    @Test
    @WithMockUser(username = "1")
    void testRestartMcpServer_Success() {
        // 准备测试数据
        McpServerInstance instance = new McpServerInstance();
        instance.setSandboxId(TEST_SANDBOX_ID);
        instance.setConfigurationId(TEST_CONFIG_ID);
        instance.setUserId(TEST_USER_ID);
        
        when(lifecycleManager.restartServer(eq(TEST_SANDBOX_ID), eq(TEST_USER_ID)))
                .thenReturn(Mono.just(instance));
        
        // 执行测试
        webTestClient.post()
                .uri("/api/mcp/sse/servers/{sandboxId}/restart", TEST_SANDBOX_ID)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.message").isEqualTo("服务器重启成功")
                .jsonPath("$.instance.sandboxId").isEqualTo(TEST_SANDBOX_ID);
    }
    
    @Test
    @WithMockUser(username = "1")
    void testGetUserMcpServers_Success() {
        // 准备测试数据
        McpServerInstance instance1 = new McpServerInstance();
        instance1.setSandboxId("sandbox-1");
        instance1.setUserId(TEST_USER_ID);
        
        McpServerInstance instance2 = new McpServerInstance();
        instance2.setSandboxId("sandbox-2");
        instance2.setUserId(TEST_USER_ID);
        
        when(lifecycleManager.getUserServers(eq(TEST_USER_ID)))
                .thenReturn(Flux.just(instance1, instance2));
        
        // 执行测试
        webTestClient.get()
                .uri("/api/mcp/sse/servers")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.success").isEqualTo(true)
                .jsonPath("$.count").isEqualTo(2)
                .jsonPath("$.servers").isArray()
                .jsonPath("$.servers[0].sandboxId").isEqualTo("sandbox-1")
                .jsonPath("$.servers[1].sandboxId").isEqualTo("sandbox-2");
    }
    
    @Test
    void testUnauthorizedAccess() {
        // 测试未认证访问
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/events", TEST_SANDBOX_ID)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .exchange()
                .expectStatus().isUnauthorized();
    }
}
