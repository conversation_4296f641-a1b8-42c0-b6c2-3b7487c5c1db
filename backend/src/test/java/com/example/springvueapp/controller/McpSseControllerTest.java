package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.service.McpSseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * McpSseController 单元测试
 */
@WebFluxTest(McpSseController.class)
class McpSseControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private McpSseService mcpSseService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(username = "testuser")
    void testStreamMcpEvents_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        Flux<ServerSentEvent<String>> mockEventStream = Flux.just(
                ServerSentEvent.<String>builder()
                        .event("mcp-message")
                        .data("{\"type\":\"response\",\"id\":\"1\",\"result\":{}}")
                        .build()
        );
        
        when(mcpSseService.streamMcpEvents(eq(sandboxId), eq(userId)))
                .thenReturn(mockEventStream);

        // When & Then
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/events", sandboxId)
                .header("X-User-Id", "1")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .exchange()
                .expectStatus().isOk()
                .expectHeader().contentTypeCompatibleWith(MediaType.TEXT_EVENT_STREAM);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testSendMcpRequest_Success() throws Exception {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        JsonRpcRequest request = new JsonRpcRequest("1", "tools/list", Map.of());
        
        Map<String, Object> expectedResponse = Map.of(
                "status", "sent",
                "requestId", "1"
        );
        
        when(mcpSseService.sendMcpRequest(eq(sandboxId), eq(userId), any(JsonRpcRequest.class)))
                .thenReturn(Mono.just(expectedResponse));

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", sandboxId)
                .header("X-User-Id", "1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(request))
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("sent")
                .jsonPath("$.requestId").isEqualTo("1");
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetConnectionStatus_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        Map<String, Object> expectedStatus = Map.of(
                "status", "connected",
                "connected", true,
                "sandboxId", sandboxId
        );
        
        when(mcpSseService.getConnectionStatus(eq(sandboxId), eq(userId)))
                .thenReturn(Mono.just(expectedStatus));

        // When & Then
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/status", sandboxId)
                .header("X-User-Id", "1")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("connected")
                .jsonPath("$.connected").isEqualTo(true)
                .jsonPath("$.sandboxId").isEqualTo(sandboxId);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testPingConnection_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        Map<String, Object> expectedResult = Map.of(
                "status", "pong",
                "latency", 50L,
                "timestamp", System.currentTimeMillis()
        );
        
        when(mcpSseService.pingConnection(eq(sandboxId), eq(userId)))
                .thenReturn(Mono.just(expectedResult));

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/ping", sandboxId)
                .header("X-User-Id", "1")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("pong")
                .jsonPath("$.latency").isEqualTo(50);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testStartMcpServer_Success() {
        // Given
        Long configId = 1L;
        Long userId = 1L;
        String sandboxId = "test-sandbox-456";
        
        Map<String, Object> expectedResult = Map.of(
                "status", "started",
                "sandboxId", sandboxId,
                "configId", configId
        );
        
        when(mcpSseService.startMcpServer(eq(configId), eq(userId)))
                .thenReturn(Mono.just(expectedResult));

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/servers/{configId}/start", configId)
                .header("X-User-Id", "1")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("started")
                .jsonPath("$.sandboxId").isEqualTo(sandboxId)
                .jsonPath("$.configId").isEqualTo(configId);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testStopMcpServer_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        
        Map<String, Object> expectedResult = Map.of(
                "status", "stopped",
                "sandboxId", sandboxId
        );
        
        when(mcpSseService.stopMcpServer(eq(sandboxId), eq(userId)))
                .thenReturn(Mono.just(expectedResult));

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/servers/{sandboxId}/stop", sandboxId)
                .header("X-User-Id", "1")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("stopped")
                .jsonPath("$.sandboxId").isEqualTo(sandboxId);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testRestartMcpServer_Success() {
        // Given
        String sandboxId = "test-sandbox-123";
        Long userId = 1L;
        String newSandboxId = "test-sandbox-456";
        
        Map<String, Object> expectedResult = Map.of(
                "status", "restarted",
                "sandboxId", newSandboxId,
                "oldSandboxId", sandboxId
        );
        
        when(mcpSseService.restartMcpServer(eq(sandboxId), eq(userId)))
                .thenReturn(Mono.just(expectedResult));

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/servers/{sandboxId}/restart", sandboxId)
                .header("X-User-Id", "1")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("restarted")
                .jsonPath("$.sandboxId").isEqualTo(newSandboxId);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testGetUserServers_Success() {
        // Given
        Long userId = 1L;
        
        Flux<Map<String, Object>> mockServers = Flux.just(
                Map.of("sandboxId", "sandbox-1", "status", "running", "configId", 1L),
                Map.of("sandboxId", "sandbox-2", "status", "stopped", "configId", 2L)
        );
        
        when(mcpSseService.getUserServers(eq(userId)))
                .thenReturn(mockServers);

        // When & Then
        webTestClient.get()
                .uri("/api/mcp/sse/servers")
                .header("X-User-Id", "1")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(Map.class)
                .hasSize(2);
    }

    @Test
    @WithMockUser(username = "testuser")
    void testSendMcpRequest_MissingUserId() throws Exception {
        // Given
        String sandboxId = "test-sandbox-123";
        JsonRpcRequest request = new JsonRpcRequest("1", "tools/list", Map.of());

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", sandboxId)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(objectMapper.writeValueAsString(request))
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    @WithMockUser(username = "testuser")
    void testSendMcpRequest_InvalidJson() {
        // Given
        String sandboxId = "test-sandbox-123";

        // When & Then
        webTestClient.post()
                .uri("/api/mcp/sse/{sandboxId}/send", sandboxId)
                .header("X-User-Id", "1")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue("invalid json")
                .exchange()
                .expectStatus().isBadRequest();
    }

    @Test
    void testStreamMcpEvents_Unauthorized() {
        // Given
        String sandboxId = "test-sandbox-123";

        // When & Then
        webTestClient.get()
                .uri("/api/mcp/sse/{sandboxId}/events", sandboxId)
                .header("X-User-Id", "1")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .exchange()
                .expectStatus().isUnauthorized();
    }
}
