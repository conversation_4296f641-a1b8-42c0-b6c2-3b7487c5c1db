package com.example.springvueapp.mcp.transport.stdio;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.mcp.transport.McpTransport;
import com.example.springvueapp.sandbox.SandboxInstance;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 基于 stdio 的 MCP 传输实现
 */
public class StdioTransport implements McpTransport {
    
    private static final Logger log = LoggerFactory.getLogger(StdioTransport.class);
    
    private final SandboxInstance sandboxInstance;
    private final ObjectMapper objectMapper;
    
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final Sinks.Many<JsonRpcMessage> messageSink;
    private final Sinks.Many<Throwable> errorSink;
    private final Sinks.Many<Boolean> connectionStatusSink;
    
    public StdioTransport(SandboxInstance sandboxInstance) {
        this.sandboxInstance = sandboxInstance;
        this.objectMapper = new ObjectMapper();
        
        // 初始化Sinks
        this.messageSink = Sinks.many().multicast().onBackpressureBuffer();
        this.errorSink = Sinks.many().multicast().onBackpressureBuffer();
        this.connectionStatusSink = Sinks.many().multicast().onBackpressureBuffer();
    }
    
    @Override
    public String getType() {
        return "stdio";
    }
    
    @Override
    public Mono<Void> connect() {
        if (connected.get()) {
            return Mono.empty();
        }
        
        return Mono.fromRunnable(() -> {
            log.info("连接到stdio MCP服务器");
            
            // 启动stdout监听
            startStdoutListener();
            
            connected.set(true);
            connectionStatusSink.tryEmitNext(true);
            log.info("stdio MCP连接已建立");
        });
    }
    
    @Override
    public Mono<Void> disconnect() {
        if (!connected.get()) {
            return Mono.empty();
        }
        
        return Mono.fromRunnable(() -> {
            log.info("断开stdio MCP连接");
            connected.set(false);
            connectionStatusSink.tryEmitNext(false);
            messageSink.tryEmitComplete();
            log.info("stdio MCP连接已断开");
        });
    }
    
    @Override
    public boolean isConnected() {
        return connected.get() && sandboxInstance.isRunning();
    }
    
    @Override
    public Mono<Void> sendRequest(JsonRpcRequest request) {
        return sendMessage(request);
    }
    
    @Override
    public Mono<Void> sendResponse(JsonRpcResponse response) {
        return sendMessage(response);
    }
    
    @Override
    public Mono<Void> sendNotification(JsonRpcNotification notification) {
        return sendMessage(notification);
    }
    
    private Mono<Void> sendMessage(Object message) {
        if (!isConnected()) {
            return Mono.error(new IllegalStateException("stdio传输未连接"));
        }
        
        return Mono.fromRunnable(() -> {
            try {
                String json = objectMapper.writeValueAsString(message);
                String messageWithNewline = json + "\n";
                
                log.debug("发送stdio消息: {}", json);
                sandboxInstance.writeToStdin(messageWithNewline).block();
                
            } catch (JsonProcessingException e) {
                log.error("序列化消息失败", e);
                errorSink.tryEmitNext(e);
                throw new RuntimeException("序列化消息失败", e);
            }
        });
    }
    
    @Override
    public Flux<JsonRpcMessage> receiveMessages() {
        return messageSink.asFlux();
    }
    
    @Override
    public Flux<Throwable> getErrors() {
        return errorSink.asFlux();
    }
    
    @Override
    public Flux<Boolean> getConnectionStatus() {
        return connectionStatusSink.asFlux();
    }
    
    private void startStdoutListener() {
        sandboxInstance.getStdoutFlux()
                .doOnNext(this::handleStdoutLine)
                .doOnError(this::handleStdoutError)
                .doOnComplete(this::handleStdoutComplete)
                .subscribe();
    }
    
    private void handleStdoutLine(String line) {
        try {
            if (line.trim().isEmpty()) {
                return;
            }
            
            log.debug("接收到stdout消息: {}", line);
            
            // 解析JSON-RPC消息
            JsonRpcMessage message = parseJsonRpcMessage(line);
            if (message != null) {
                messageSink.tryEmitNext(message);
            }
        } catch (Exception e) {
            log.error("处理stdout消息失败: {}", line, e);
            errorSink.tryEmitNext(e);
        }
    }
    
    private void handleStdoutError(Throwable error) {
        log.error("stdout流错误", error);
        connected.set(false);
        connectionStatusSink.tryEmitNext(false);
        errorSink.tryEmitNext(error);
    }
    
    private void handleStdoutComplete() {
        log.info("stdout流完成");
        connected.set(false);
        connectionStatusSink.tryEmitNext(false);
        messageSink.tryEmitComplete();
    }
    
    private JsonRpcMessage parseJsonRpcMessage(String json) {
        try {
            // 首先尝试解析为通用的JsonRpcMessage
            if (json.contains("\"id\"") && json.contains("\"result\"")) {
                return objectMapper.readValue(json, JsonRpcResponse.class);
            } else if (json.contains("\"id\"") && json.contains("\"method\"")) {
                return objectMapper.readValue(json, JsonRpcRequest.class);
            } else if (json.contains("\"method\"")) {
                return objectMapper.readValue(json, JsonRpcNotification.class);
            }
            
            log.warn("无法识别的JSON-RPC消息格式: {}", json);
            return null;
        } catch (JsonProcessingException e) {
            log.error("解析JSON-RPC消息失败: {}", json, e);
            return null;
        }
    }
}
