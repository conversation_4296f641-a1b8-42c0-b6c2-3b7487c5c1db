package com.example.springvueapp.mcp.service;

import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.model.McpServerStatus;
import com.example.springvueapp.service.McpConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP服务器生命周期管理器
 * 负责管理MCP服务器的启动、停止、重启和监控
 */
@Service
public class McpServerLifecycleManager {
    
    private static final Logger log = LoggerFactory.getLogger(McpServerLifecycleManager.class);
    
    private final McpServiceManager mcpServiceManager;
    private final McpConfigurationService configurationService;
    
    // 服务器实例状态缓存
    private final Map<String, McpServerStatus> serverStatusCache = new ConcurrentHashMap<>();
    
    // 自动重启配置
    private final Map<String, Boolean> autoRestartEnabled = new ConcurrentHashMap<>();
    
    public McpServerLifecycleManager(McpServiceManager mcpServiceManager,
                                   McpConfigurationService configurationService) {
        this.mcpServiceManager = mcpServiceManager;
        this.configurationService = configurationService;
    }
    
    /**
     * 启动MCP服务器
     * 
     * @param configId 配置ID
     * @param userId 用户ID
     * @return 启动的服务器实例
     */
    public Mono<McpServerInstance> startServer(Long configId, Long userId) {
        log.info("启动MCP服务器: configId={}, userId={}", configId, userId);
        
        return configurationService.getConfiguration(configId, userId)
                .flatMap(config -> {
                    if (!config.getEnabled()) {
                        return Mono.error(new RuntimeException("配置未启用"));
                    }
                    
                    return mcpServiceManager.startMcpServer(config, userId)
                            .doOnSuccess(instance -> {
                                // 缓存服务器状态
                                McpServerStatus status = new McpServerStatus(instance.getSandboxId(), true);
                                serverStatusCache.put(instance.getSandboxId(), status);
                                
                                // 设置自动重启
                                autoRestartEnabled.put(instance.getSandboxId(), config.getAutoRestart());
                                
                                log.info("MCP服务器启动成功: sandboxId={}", instance.getSandboxId());
                            });
                })
                .doOnError(error -> log.error("启动MCP服务器失败: configId={}", configId, error));
    }
    
    /**
     * 停止MCP服务器
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 停止完成信号
     */
    public Mono<Void> stopServer(String sandboxId, Long userId) {
        log.info("停止MCP服务器: sandboxId={}, userId={}", sandboxId, userId);
        
        return mcpServiceManager.stopMcpServer(sandboxId, userId)
                .doOnSuccess(v -> {
                    // 更新状态缓存
                    McpServerStatus status = serverStatusCache.get(sandboxId);
                    if (status != null) {
                        status.setConnected(false);
                        status.setLastActivity(LocalDateTime.now());
                    }
                    
                    // 禁用自动重启
                    autoRestartEnabled.put(sandboxId, false);
                    
                    log.info("MCP服务器停止成功: sandboxId={}", sandboxId);
                })
                .doOnError(error -> log.error("停止MCP服务器失败: sandboxId={}", sandboxId, error));
    }
    
    /**
     * 重启MCP服务器
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 重启后的服务器实例
     */
    public Mono<McpServerInstance> restartServer(String sandboxId, Long userId) {
        log.info("重启MCP服务器: sandboxId={}, userId={}", sandboxId, userId);
        
        return mcpServiceManager.restartMcpServer(sandboxId, userId)
                .doOnSuccess(instance -> {
                    // 更新状态缓存
                    McpServerStatus status = new McpServerStatus(instance.getSandboxId(), true);
                    serverStatusCache.put(instance.getSandboxId(), status);
                    
                    log.info("MCP服务器重启成功: sandboxId={}", instance.getSandboxId());
                })
                .doOnError(error -> log.error("重启MCP服务器失败: sandboxId={}", sandboxId, error));
    }
    
    /**
     * 获取服务器状态
     * 
     * @param sandboxId 沙箱ID
     * @param userId 用户ID
     * @return 服务器状态
     */
    public Mono<McpServerStatus> getServerStatus(String sandboxId, Long userId) {
        return mcpServiceManager.getMcpServerStatus(sandboxId, userId)
                .doOnSuccess(status -> {
                    // 更新缓存
                    serverStatusCache.put(sandboxId, status);
                });
    }
    
    /**
     * 获取用户的所有服务器实例
     * 
     * @param userId 用户ID
     * @return 服务器实例列表
     */
    public Flux<McpServerInstance> getUserServers(Long userId) {
        return mcpServiceManager.getUserMcpServers(userId);
    }
    
    /**
     * 批量启动用户的所有启用配置
     * 
     * @param userId 用户ID
     * @return 启动结果
     */
    public Flux<McpServerInstance> startAllEnabledServers(Long userId) {
        return configurationService.getConfigurations(userId)
                .filter(McpServerConfiguration::getEnabled)
                .flatMap(config -> startServer(config.getId(), userId)
                        .onErrorResume(error -> {
                            log.warn("启动服务器失败: configId={}, error={}", 
                                    config.getId(), error.getMessage());
                            return Mono.empty();
                        }));
    }
    
    /**
     * 批量停止用户的所有服务器
     * 
     * @param userId 用户ID
     * @return 停止完成信号
     */
    public Mono<Void> stopAllUserServers(Long userId) {
        return getUserServers(userId)
                .flatMap(instance -> stopServer(instance.getSandboxId(), userId)
                        .onErrorResume(error -> {
                            log.warn("停止服务器失败: sandboxId={}, error={}", 
                                    instance.getSandboxId(), error.getMessage());
                            return Mono.empty();
                        }))
                .then();
    }
    
    /**
     * 定期健康检查和自动重启
     */
    @Scheduled(fixedRate = 60000) // 每分钟检查一次
    public void healthCheckAndAutoRestart() {
        log.debug("开始MCP服务器健康检查");
        
        serverStatusCache.entrySet().forEach(entry -> {
            String sandboxId = entry.getKey();
            McpServerStatus cachedStatus = entry.getValue();
            
            // 检查是否需要自动重启
            Boolean autoRestart = autoRestartEnabled.get(sandboxId);
            if (autoRestart != null && autoRestart && !cachedStatus.isConnected()) {
                log.info("检测到服务器离线，尝试自动重启: sandboxId={}", sandboxId);
                
                // 这里需要获取用户ID，简化处理
                // 实际实现中应该从实例记录中获取用户ID
                Long userId = 1L; // 临时硬编码
                
                restartServer(sandboxId, userId)
                        .subscribe(
                            instance -> log.info("自动重启成功: sandboxId={}", sandboxId),
                            error -> log.error("自动重启失败: sandboxId={}", sandboxId, error)
                        );
            }
        });
        
        log.debug("MCP服务器健康检查完成");
    }
    
    /**
     * 清理过期的状态缓存
     */
    @Scheduled(fixedRate = 300000) // 每5分钟清理一次
    public void cleanupExpiredCache() {
        log.debug("开始清理过期的服务器状态缓存");
        
        LocalDateTime cutoff = LocalDateTime.now().minusHours(1);
        
        serverStatusCache.entrySet().removeIf(entry -> {
            McpServerStatus status = entry.getValue();
            return status.getLastActivity() != null && status.getLastActivity().isBefore(cutoff);
        });
        
        log.debug("服务器状态缓存清理完成，当前缓存大小: {}", serverStatusCache.size());
    }
    
    /**
     * 获取服务器统计信息
     * 
     * @return 统计信息
     */
    public Map<String, Object> getServerStats() {
        long totalServers = serverStatusCache.size();
        long connectedServers = serverStatusCache.values().stream()
                .mapToLong(status -> status.isConnected() ? 1 : 0)
                .sum();
        long autoRestartServers = autoRestartEnabled.values().stream()
                .mapToLong(enabled -> enabled ? 1 : 0)
                .sum();
        
        return Map.of(
            "totalServers", totalServers,
            "connectedServers", connectedServers,
            "disconnectedServers", totalServers - connectedServers,
            "autoRestartEnabled", autoRestartServers,
            "lastUpdate", LocalDateTime.now()
        );
    }
    
    /**
     * 强制清理所有服务器
     * 用于应用程序关闭时的清理
     */
    public void shutdownAllServers() {
        log.info("开始关闭所有MCP服务器");
        
        serverStatusCache.keySet().forEach(sandboxId -> {
            try {
                // 这里需要获取用户ID，简化处理
                Long userId = 1L; // 临时硬编码
                stopServer(sandboxId, userId).block();
            } catch (Exception e) {
                log.warn("关闭服务器时发生错误: sandboxId={}", sandboxId, e);
            }
        });
        
        serverStatusCache.clear();
        autoRestartEnabled.clear();
        
        log.info("所有MCP服务器已关闭");
    }
}
