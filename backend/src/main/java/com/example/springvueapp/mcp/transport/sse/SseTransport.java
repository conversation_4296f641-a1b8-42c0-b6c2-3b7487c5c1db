package com.example.springvueapp.mcp.transport.sse;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcNotification;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.protocol.JsonRpcResponse;
import com.example.springvueapp.mcp.transport.McpTransport;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 基于 Server-Sent Events 的 MCP 传输实现
 */
public class SseTransport implements McpTransport {
    
    private static final Logger log = LoggerFactory.getLogger(SseTransport.class);
    
    private final String serverUrl;
    private final Map<String, String> headers;
    private final WebClient webClient;
    private final ObjectMapper objectMapper;
    
    private final AtomicBoolean connected = new AtomicBoolean(false);
    private final Sinks.Many<JsonRpcMessage> messageSink;
    private final Sinks.Many<Throwable> errorSink;
    private final Sinks.Many<Boolean> connectionStatusSink;
    
    public SseTransport(String serverUrl, Map<String, String> headers) {
        this.serverUrl = serverUrl;
        this.headers = headers != null ? headers : Map.of();
        this.objectMapper = new ObjectMapper();
        
        // 创建WebClient
        WebClient.Builder builder = WebClient.builder()
                .baseUrl(serverUrl)
                .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(1024 * 1024)); // 1MB
        
        // 添加默认头部
        if (!this.headers.isEmpty()) {
            builder.defaultHeaders(httpHeaders -> {
                this.headers.forEach(httpHeaders::add);
            });
        }
        
        this.webClient = builder.build();
        
        // 初始化Sinks
        this.messageSink = Sinks.many().multicast().onBackpressureBuffer();
        this.errorSink = Sinks.many().multicast().onBackpressureBuffer();
        this.connectionStatusSink = Sinks.many().multicast().onBackpressureBuffer();
    }
    
    @Override
    public String getType() {
        return "sse";
    }
    
    @Override
    public Mono<Void> connect() {
        if (connected.get()) {
            return Mono.empty();
        }
        
        return Mono.fromRunnable(() -> {
            log.info("连接到SSE MCP服务器: {}", serverUrl);
            
            // 启动SSE连接
            startSseConnection();
            
            connected.set(true);
            connectionStatusSink.tryEmitNext(true);
            log.info("SSE MCP连接已建立");
        });
    }
    
    @Override
    public Mono<Void> disconnect() {
        if (!connected.get()) {
            return Mono.empty();
        }
        
        return Mono.fromRunnable(() -> {
            log.info("断开SSE MCP连接");
            connected.set(false);
            connectionStatusSink.tryEmitNext(false);
            messageSink.tryEmitComplete();
            log.info("SSE MCP连接已断开");
        });
    }
    
    @Override
    public boolean isConnected() {
        return connected.get();
    }
    
    @Override
    public Mono<Void> sendRequest(JsonRpcRequest request) {
        return sendMessage(request);
    }
    
    @Override
    public Mono<Void> sendResponse(JsonRpcResponse response) {
        return sendMessage(response);
    }
    
    @Override
    public Mono<Void> sendNotification(JsonRpcNotification notification) {
        return sendMessage(notification);
    }
    
    private Mono<Void> sendMessage(Object message) {
        if (!connected.get()) {
            return Mono.error(new IllegalStateException("SSE传输未连接"));
        }
        
        return Mono.fromCallable(() -> {
            try {
                String json = objectMapper.writeValueAsString(message);
                log.debug("发送SSE消息: {}", json);
                return json;
            } catch (JsonProcessingException e) {
                throw new RuntimeException("序列化消息失败", e);
            }
        })
        .flatMap(json -> 
            webClient.post()
                .uri("/mcp")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(json)
                .retrieve()
                .bodyToMono(Void.class)
        )
        .doOnError(error -> {
            log.error("发送SSE消息失败", error);
            errorSink.tryEmitNext(error);
        });
    }
    
    @Override
    public Flux<JsonRpcMessage> receiveMessages() {
        return messageSink.asFlux();
    }
    
    @Override
    public Flux<Throwable> getErrors() {
        return errorSink.asFlux();
    }
    
    @Override
    public Flux<Boolean> getConnectionStatus() {
        return connectionStatusSink.asFlux();
    }
    
    private void startSseConnection() {
        webClient.get()
                .uri("/sse")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .retrieve()
                .bodyToFlux(String.class)
                .retry(3)
                .timeout(Duration.ofSeconds(30))
                .subscribe(
                    this::handleSseMessage,
                    this::handleSseError,
                    this::handleSseComplete
                );
    }
    
    private void handleSseMessage(String data) {
        try {
            log.debug("接收到SSE消息: {}", data);
            
            // 解析JSON-RPC消息
            JsonRpcMessage message = parseJsonRpcMessage(data);
            if (message != null) {
                messageSink.tryEmitNext(message);
            }
        } catch (Exception e) {
            log.error("处理SSE消息失败: {}", data, e);
            errorSink.tryEmitNext(e);
        }
    }
    
    private void handleSseError(Throwable error) {
        log.error("SSE连接错误", error);
        connected.set(false);
        connectionStatusSink.tryEmitNext(false);
        errorSink.tryEmitNext(error);
        
        // 尝试重连
        if (error instanceof java.net.ConnectException || 
            error instanceof java.util.concurrent.TimeoutException) {
            log.info("尝试重新连接SSE...");
            Mono.delay(Duration.ofSeconds(5))
                .then(connect())
                .subscribe();
        }
    }
    
    private void handleSseComplete() {
        log.info("SSE连接完成");
        connected.set(false);
        connectionStatusSink.tryEmitNext(false);
        messageSink.tryEmitComplete();
    }
    
    private JsonRpcMessage parseJsonRpcMessage(String json) {
        try {
            // 首先尝试解析为通用的JsonRpcMessage
            if (json.contains("\"id\"") && json.contains("\"result\"")) {
                return objectMapper.readValue(json, JsonRpcResponse.class);
            } else if (json.contains("\"id\"") && json.contains("\"method\"")) {
                return objectMapper.readValue(json, JsonRpcRequest.class);
            } else if (json.contains("\"method\"")) {
                return objectMapper.readValue(json, JsonRpcNotification.class);
            }
            
            log.warn("无法识别的JSON-RPC消息格式: {}", json);
            return null;
        } catch (JsonProcessingException e) {
            log.error("解析JSON-RPC消息失败: {}", json, e);
            return null;
        }
    }
}
