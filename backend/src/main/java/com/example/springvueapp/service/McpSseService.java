package com.example.springvueapp.service;

import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.mcp.service.McpServiceManager;
import com.example.springvueapp.mcp.transport.sse.SseConnectionManager;
import com.example.springvueapp.mcp.transport.sse.SseServerTransport;
import com.example.springvueapp.model.McpServerStatus;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * MCP Server-Sent Events 服务
 * 管理基于SSE协议的MCP服务器连接和消息传输
 */
@Service
public class McpSseService {
    
    private static final Logger log = LoggerFactory.getLogger(McpSseService.class);
    
    private final McpServiceManager mcpServiceManager;
    private final SseConnectionManager sseConnectionManager;
    private final ObjectMapper objectMapper;

    public McpSseService(McpServiceManager mcpServiceManager,
                        SseConnectionManager sseConnectionManager,
                        ObjectMapper objectMapper) {
        this.mcpServiceManager = mcpServiceManager;
        this.sseConnectionManager = sseConnectionManager;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 为指定的MCP服务器创建SSE事件流
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @return SSE消息流
     */
    public Flux<String> streamMcpEvents(String sandboxId, Long userId) {
        return mcpServiceManager.hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .flatMap(hasAccess -> sseConnectionManager.createConnection(sandboxId, userId))
                .flatMapMany(transport -> {
                    log.info("建立SSE连接: {} (用户: {})", sandboxId, userId);

                    // 设置MCP消息转发
                    setupMcpMessageForwarding(sandboxId, userId, transport);

                    // 返回SSE消息流
                    return transport.getOutgoingMessageStream()
                            .doOnCancel(() -> {
                                log.debug("SSE连接取消: sandboxId={}, userId={}", sandboxId, userId);
                                sseConnectionManager.closeConnection(sandboxId, userId).subscribe();
                            })
                            .doOnComplete(() -> {
                                log.debug("SSE连接完成: sandboxId={}, userId={}", sandboxId, userId);
                                sseConnectionManager.closeConnection(sandboxId, userId).subscribe();
                            });
                })
                .onErrorResume(error -> {
                    log.error("创建SSE事件流失败: sandboxId={}, userId={}", sandboxId, userId, error);
                    return Flux.just(createErrorMessage("连接失败: " + error.getMessage()));
                });
    }
    
    /**
     * 向指定的MCP服务器发送JSON-RPC请求
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @param request JSON-RPC请求
     * @return 发送结果
     */
    public Mono<Void> sendMcpRequest(String sandboxId, Long userId, JsonRpcRequest request) {
        return mcpServiceManager.sendMcpRequest(sandboxId, userId, request)
                .doOnSuccess(response -> {
                    log.debug("MCP请求发送成功: sandboxId={}, method={}",
                            sandboxId, request.getMethod());
                })
                .doOnError(error -> {
                    log.error("发送MCP请求失败: sandboxId={}, method={}",
                            sandboxId, request.getMethod(), error);
                })
                .then();
    }
    
    /**
     * 获取MCP服务器状态
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @return 服务器状态
     */
    public Mono<McpServerStatus> getMcpServerStatus(String sandboxId, Long userId) {
        return mcpServiceManager.getMcpServerStatus(sandboxId, userId)
                .map(status -> {
                    // 更新活跃连接数
                    int activeConnections = sseConnectionManager.getSandboxActiveConnectionCount(sandboxId);
                    status.setActiveConnections(activeConnections);

                    return status;
                });
    }
    
    /**
     * 心跳检测
     *
     * @param sandboxId MCP服务器沙箱ID
     * @param userId 用户ID
     * @return 延迟时间（毫秒）
     */
    public Mono<Long> pingMcpServer(String sandboxId, Long userId) {
        return mcpServiceManager.hasAccess(sandboxId, userId)
                .filter(hasAccess -> hasAccess)
                .switchIfEmpty(Mono.error(new RuntimeException("访问被拒绝")))
                .flatMap(hasAccess -> {
                    long startTime = System.currentTimeMillis();

                    // 简单的ping实现：检查服务器状态
                    return mcpServiceManager.getMcpServerStatus(sandboxId, userId)
                            .map(status -> {
                                long latency = System.currentTimeMillis() - startTime;
                                return latency;
                            });
                });
    }
    

    
    /**
     * 设置MCP消息转发
     */
    private void setupMcpMessageForwarding(String sandboxId, Long userId, SseServerTransport transport) {
        // 订阅MCP消息流并转发到SSE连接
        mcpServiceManager.getMcpMessageStream(sandboxId, userId)
                .subscribe(
                    message -> {
                        // 将MCP消息转发到SSE传输
                        try {
                            transport.receiveFromClient(message).subscribe();
                        } catch (Exception e) {
                            log.error("转发MCP消息失败: sandboxId={}", sandboxId, e);
                        }
                    },
                    error -> {
                        log.error("MCP消息流错误: sandboxId={}", sandboxId, error);
                        String errorMessage = createErrorMessage("MCP消息流错误: " + error.getMessage());
                        transport.sendNotification(null).subscribe(); // 发送错误通知
                    },
                    () -> log.debug("MCP消息流完成: sandboxId={}", sandboxId)
                );
    }
    
    /**
     * 创建错误消息
     */
    private String createErrorMessage(String errorMessage) {
        try {
            Map<String, Object> error = Map.of(
                "type", "error",
                "message", errorMessage,
                "timestamp", System.currentTimeMillis()
            );
            return objectMapper.writeValueAsString(error);
        } catch (JsonProcessingException e) {
            log.error("创建错误消息失败", e);
            return "{\"type\":\"error\",\"message\":\"内部错误\"}";
        }
    }
    
    /**
     * 更新服务器状态
     */
    private void updateServerStatus(String sandboxId, boolean connected) {
        McpServerStatus status = serverStatuses.computeIfAbsent(sandboxId, k -> new McpServerStatus());
        status.setSandboxId(sandboxId);
        status.setConnected(connected);
        status.setLastActivity(LocalDateTime.now());
    }
}
