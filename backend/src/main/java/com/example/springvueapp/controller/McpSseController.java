package com.example.springvueapp.controller;

import com.example.springvueapp.mcp.protocol.JsonRpcMessage;
import com.example.springvueapp.mcp.protocol.JsonRpcRequest;
import com.example.springvueapp.service.McpSseService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Map;

/**
 * MCP Server-Sent Events 控制器
 * 为外部MCP客户端提供基于SSE协议的MCP服务接口
 */
@RestController
@RequestMapping("/api/mcp/sse")
public class McpSseController {
    
    private static final Logger log = LoggerFactory.getLogger(McpSseController.class);
    
    private final McpSseService mcpSseService;
    
    public McpSseController(McpSseService mcpSseService) {
        this.mcpSseService = mcpSseService;
    }
    
    /**
     * 建立SSE连接到指定的MCP服务器实例
     * URL格式: /api/mcp/sse/{sandboxId}/events
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param authentication 用户认证信息
     * @return SSE事件流
     */
    @GetMapping(value = "/{sandboxId}/events", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent<String>> streamMcpEvents(
            @PathVariable String sandboxId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.info("建立SSE连接到MCP服务器: {} (用户: {})", sandboxId, userId);
        
        return mcpSseService.streamMcpEvents(sandboxId, userId)
                .map(message -> ServerSentEvent.<String>builder()
                        .id(String.valueOf(System.currentTimeMillis()))
                        .event("mcp-message")
                        .data(message)
                        .build())
                .doOnSubscribe(subscription -> 
                    log.debug("SSE订阅开始: sandboxId={}, userId={}", sandboxId, userId))
                .doOnCancel(() -> 
                    log.debug("SSE连接取消: sandboxId={}, userId={}", sandboxId, userId))
                .doOnComplete(() -> 
                    log.debug("SSE连接完成: sandboxId={}, userId={}", sandboxId, userId))
                .doOnError(error -> 
                    log.error("SSE连接错误: sandboxId={}, userId={}", sandboxId, userId, error));
    }
    
    /**
     * 向指定的MCP服务器发送JSON-RPC请求
     * URL格式: /api/mcp/sse/{sandboxId}/send
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param request JSON-RPC请求
     * @param authentication 用户认证信息
     * @return 发送结果
     */
    @PostMapping("/{sandboxId}/send")
    public Mono<Map<String, Object>> sendMcpRequest(
            @PathVariable String sandboxId,
            @RequestBody JsonRpcRequest request,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.debug("发送MCP请求到服务器: {} (用户: {}, 方法: {})", 
                sandboxId, userId, request.getMethod());
        
        return mcpSseService.sendMcpRequest(sandboxId, userId, request)
                .map(response -> Map.of(
                    "success", true,
                    "message", "请求已发送",
                    "requestId", request.getId() != null ? request.getId() : "unknown"
                ))
                .onErrorResume(error -> {
                    log.error("发送MCP请求失败: sandboxId={}, method={}", 
                            sandboxId, request.getMethod(), error);
                    return Mono.just(Map.of(
                        "success", false,
                        "error", error.getMessage()
                    ));
                });
    }
    
    /**
     * 获取MCP服务器连接状态
     * URL格式: /api/mcp/sse/{sandboxId}/status
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param authentication 用户认证信息
     * @return 连接状态信息
     */
    @GetMapping("/{sandboxId}/status")
    public Mono<Map<String, Object>> getMcpServerStatus(
            @PathVariable String sandboxId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        log.debug("获取MCP服务器状态: {} (用户: {})", sandboxId, userId);
        
        return mcpSseService.getMcpServerStatus(sandboxId, userId)
                .map(status -> Map.of(
                    "sandboxId", sandboxId,
                    "connected", status.isConnected(),
                    "lastActivity", status.getLastActivity(),
                    "activeConnections", status.getActiveConnections()
                ))
                .onErrorResume(error -> {
                    log.error("获取MCP服务器状态失败: sandboxId={}", sandboxId, error);
                    return Mono.just(Map.of(
                        "sandboxId", sandboxId,
                        "connected", false,
                        "error", error.getMessage()
                    ));
                });
    }
    
    /**
     * 心跳检测端点
     * URL格式: /api/mcp/sse/{sandboxId}/ping
     * 
     * @param sandboxId MCP服务器沙箱ID
     * @param authentication 用户认证信息
     * @return 心跳响应
     */
    @PostMapping("/{sandboxId}/ping")
    public Mono<Map<String, Object>> pingMcpServer(
            @PathVariable String sandboxId,
            Authentication authentication) {
        
        Long userId = getUserId(authentication);
        
        return mcpSseService.pingMcpServer(sandboxId, userId)
                .map(latency -> Map.of(
                    "success", true,
                    "latency", latency,
                    "timestamp", System.currentTimeMillis()
                ))
                .timeout(Duration.ofSeconds(5))
                .onErrorResume(error -> Mono.just(Map.of(
                    "success", false,
                    "error", error.getMessage(),
                    "timestamp", System.currentTimeMillis()
                )));
    }
    
    /**
     * 从认证信息中提取用户ID
     */
    private Long getUserId(Authentication authentication) {
        // 这里需要根据实际的认证实现来获取用户ID
        // 假设用户名就是用户ID，实际项目中可能需要查询用户表
        try {
            return Long.parseLong(authentication.getName());
        } catch (NumberFormatException e) {
            // 如果用户名不是数字，可能需要通过用户服务查询
            // 这里简化处理，返回一个默认值或抛出异常
            log.warn("无法从认证信息中解析用户ID: {}", authentication.getName());
            return 1L; // 临时返回默认用户ID
        }
    }
}
